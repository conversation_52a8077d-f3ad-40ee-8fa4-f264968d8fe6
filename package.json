{"name": "claim-ui", "version": "0.1.0", "private": true, "scripts": {"dev": "set NODE_OPTIONS='--inspect' && next dev --turbopack -p 3000", "build": "next build", "start": "next start -p 3000", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "cypress run", "test:ci": "jest && cypress run"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.1", "@mui/material": "^6.4.1", "@rainbow-me/rainbowkit": "^2.2.3", "@svgr/webpack": "^8.0.1", "@tanstack/react-query": "^5.64.2", "crypto-browserify": "^3.12.0", "ethers": "^6.13.5", "https-browserify": "^1.0.0", "multiformats": "^13.3.7", "next": "15.1.6", "os-browserify": "^0.3.0", "react": "^19.0.0", "react-dom": "^19.0.0", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "viem": "^2.22.13", "wagmi": "^2.14.9"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10", "eslint": "^9", "eslint-config-next": "15.1.6", "file-loader": "^6.2.0", "pino-pretty": "^13.0.0", "postcss": "^8", "typescript": "^5"}}