<!-- FULA Staking Widget for WordPress HTML Block -->

<!-- CSS Styles -->
<style>
  #fula-staking-widget {
    font-family: 'Inter', 'Roboto', sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    border-radius: 12px;
    background-color: #f8faf8;
    box-shadow: 0 4px 12px rgba(0, 100, 0, 0.1);
    color: #333;
  }
  
  .fula-header {
    text-align: center;
    margin-bottom: 30px;
  }
  
  .fula-header h2 {
    color: #2e7d32;
    font-size: 28px;
    margin-bottom: 10px;
  }
  
  .fula-header p {
    color: #558b2f;
    font-size: 16px;
  }
  
  /* Stats boxes styles */
  .fula-stats-container {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    margin-bottom: 25px;
    flex-wrap: wrap;
  }
  
  .fula-stats-box {
    flex: 1;
    min-width: 150px;
    background-color: white;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0, 100, 0, 0.1);
    border-left: 4px solid #43a047;
    transition: transform 0.3s;
  }
  
  .fula-stats-box:hover {
    transform: translateY(-3px);
  }
  
  .fula-stats-title {
    color: #558b2f;
    font-size: 14px;
    margin-bottom: 8px;
  }
  
  .fula-stats-value {
    color: #2e7d32;
    font-size: 24px;
    font-weight: bold;
  }
  
  .fula-connect-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
  }
  
  .fula-network-selector {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
  
  .fula-network-selector label {
    margin-right: 10px;
    font-weight: 500;
    color: #2e7d32;
  }
  
  .fula-network-selector select {
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #81c784;
    background-color: white;
    color: #2e7d32;
    font-size: 14px;
  }
  
  .fula-connect-button {
    background-color: #43a047;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s;
  }
  
  .fula-connect-button:hover {
    background-color: #2e7d32;
  }
  
  .fula-connect-button:disabled {
    background-color: #a5d6a7;
    cursor: not-allowed;
  }
  
  .fula-connect-button.connected {
    background-color: #d32f2f;
    color: #fff;
  }
  
  .fula-wallet-info {
    display: none;
    background-color: #e8f5e9;
    padding: 10px 15px;
    border-radius: 6px;
    margin-bottom: 20px;
    border-left: 4px solid #43a047;
  }
  
  .fula-wallet-address {
    font-family: monospace;
    font-size: 14px;
    word-break: break-all;
  }
  
  .fula-balance {
    margin-top: 5px;
    font-weight: 500;
  }
  
  .fula-tabs {
    display: flex;
    border-bottom: 1px solid #81c784;
    margin-bottom: 20px;
    flex-wrap: wrap;
  }
  
  .fula-tab {
    padding: 10px 20px;
    cursor: pointer;
    background-color: transparent;
    border: none;
    border-bottom: 3px solid transparent;
    color: #558b2f;
    font-weight: 500;
    transition: all 0.3s;
    text-align: center;
  }
  
  .fula-tab.active {
    border-bottom: 3px solid #2e7d32;
    color: #2e7d32;
  }
  
  .fula-tab-content {
    display: none;
  }
  
  .fula-tab-content.active {
    display: block;
  }
  
  .fula-staking-options {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
  }
  
  .fula-staking-option {
    flex: 1;
    min-width: 200px;
    background-color: white;
    border: 1px solid #c8e6c9;
    border-radius: 8px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
  }
  
  .fula-staking-option:hover {
    border-color: #81c784;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 100, 0, 0.1);
  }
  
  .fula-staking-option.selected {
    border-color: #43a047;
    background-color: #e8f5e9;
  }
  
  .fula-staking-option.selected:after {
    content: "✓";
    position: absolute;
    top: 10px;
    right: 10px;
    color: #43a047;
    font-weight: bold;
  }
  
  .fula-staking-option h4 {
    color: #2e7d32;
    margin-top: 0;
    margin-bottom: 10px;
  }
  
  .fula-staking-option .apy {
    font-size: 24px;
    font-weight: bold;
    color: #43a047;
    margin-bottom: 10px;
  }
  
  .fula-staking-option .period {
    font-size: 14px;
    color: #558b2f;
  }
  
  .fula-input-group {
    margin-bottom: 20px;
  }
  
  .fula-input-group label {
    display: block;
    margin-bottom: 8px;
    color: #2e7d32;
    font-weight: 500;
  }
  
  .fula-input-wrapper {
    display: flex;
    border: 1px solid #81c784;
    border-radius: 6px;
    overflow: hidden;
  }
  
  .fula-input-wrapper input {
    flex: 1;
    padding: 10px 15px;
    border: none;
    font-size: 16px;
  }
  
  .fula-input-wrapper input:focus {
    outline: none;
  }
  
  .fula-input-wrapper .fula-max-button {
    background-color: #c8e6c9;
    border: none;
    color: #2e7d32;
    padding: 0 15px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s;
  }
  
  .fula-input-wrapper .fula-max-button:hover {
    background-color: #a5d6a7;
  }
  
  .fula-referral-input {
    margin-top: 15px;
  }
  
  .fula-action-button {
    width: 100%;
    background-color: #43a047;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    font-size: 16px;
    transition: background-color 0.3s;
    margin-bottom: 10px;
  }
  
  .fula-action-button:hover {
    background-color: #2e7d32;
  }
  
  .fula-action-button:disabled {
    background-color: #a5d6a7;
    cursor: not-allowed;
  }
  
  .fula-info-section {
    background-color: #e8f5e9;
    border-radius: 8px;
    padding: 15px;
    margin-top: 30px;
  }
  
  .fula-info-section h3 {
    color: #2e7d32;
    margin-top: 0;
    margin-bottom: 15px;
  }
  
  .fula-info-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 15px;
  }
  
  .fula-info-table th, .fula-info-table td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid #c8e6c9;
  }
  
  .fula-info-table th {
    color: #2e7d32;
    font-weight: 500;
  }
  
  .fula-stakes-list {
    margin-top: 20px;
  }
  
  .fula-stake-item {
    background-color: white;
    border: 1px solid #c8e6c9;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
  }
  
  .fula-stake-item .stake-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
  }
  
  .fula-stake-item .stake-amount {
    font-weight: bold;
    color: #2e7d32;
  }
  
  .fula-stake-item .stake-period {
    color: #558b2f;
  }
  
  .fula-stake-item .stake-details {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
  }
  
  .fula-stake-item .stake-rewards {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #43a047;
    font-weight: 500;
    margin-bottom: 10px;
    padding: 5px;
    background-color: #f1f8e9;
    border-radius: 4px;
  }
  
  .fula-stake-item .unstake-button, .fula-stake-item .claim-button {
    margin-top: 10px;
    background-color: #f1f8e9;
    border: 1px solid #81c784;
    color: #2e7d32;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    margin-right: 10px;
  }
  
  .fula-stake-item .unstake-button:hover, .fula-stake-item .claim-button:hover {
    background-color: #c8e6c9;
  }
  
  .fula-stake-item .unstake-button:disabled, .fula-stake-item .claim-button:disabled {
    background-color: #f1f8e9;
    color: #a5d6a7;
    cursor: not-allowed;
    border-color: #c8e6c9;
  }
  
  .fula-notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s;
    z-index: 1000;
    max-width: 300px;
  }
  
  .fula-notification.success {
    background-color: #43a047;
  }
  
  .fula-notification.error {
    background-color: #e53935;
  }
  
  .fula-notification.show {
    opacity: 1;
    transform: translateY(0);
  }
  
  .fula-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: white;
    animation: fula-spin 1s ease-in-out infinite;
    margin-right: 10px;
  }
  
  .fula-referrer-stats {
    background-color: #e8f5e9;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
  }
  
  .fula-referrer-stats h3 {
    color: #2e7d32;
    margin-top: 0;
    margin-bottom: 15px;
  }
  
  .fula-referrer-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
  }
  
  .fula-referrer-stat-item {
    background-color: white;
    border-radius: 6px;
    padding: 10px;
    box-shadow: 0 2px 4px rgba(0, 100, 0, 0.1);
  }
  
  .fula-referrer-stat-label {
    font-size: 12px;
    color: #558b2f;
    margin-bottom: 5px;
  }
  
  .fula-referrer-stat-value {
    font-size: 18px;
    font-weight: bold;
    color: #2e7d32;
  }
  
  .fula-referrer-claim-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: white;
    border-radius: 6px;
    padding: 10px 15px;
    margin-top: 15px;
  }
  
  .fula-referrer-claim-info {
    font-weight: 500;
    color: #2e7d32;
  }
  
  .fula-referrer-claim-button {
    background-color: #43a047;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s;
  }
  
  .fula-referrer-claim-button:hover {
    background-color: #2e7d32;
  }
  
  .fula-referrer-claim-button:disabled {
    background-color: #a5d6a7;
    cursor: not-allowed;
  }
  
  .fula-referrer-rewards-list {
    margin-top: 15px;
  }
  
  .fula-referrer-reward-item {
    background-color: white;
    border: 1px solid #c8e6c9;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
  }
  
  .fula-referrer-reward-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
  }
  
  .fula-referrer-reward-referee {
    font-weight: 500;
    color: #2e7d32;
  }
  
  .fula-referrer-reward-amount {
    font-weight: bold;
    color: #43a047;
  }
  
  .fula-referrer-reward-details {
    display: flex;
    justify-content: space-between;
    font-size: 13px;
    color: #666;
    margin-bottom: 5px;
  }
  
  .fula-referrer-reward-progress {
    height: 6px;
    background-color: #e8f5e9;
    border-radius: 3px;
    margin: 8px 0;
    overflow: hidden;
  }
  
  .fula-referrer-reward-progress-bar {
    height: 100%;
    background-color: #43a047;
    border-radius: 3px;
  }
  
  .fula-button-group {
    display: flex;
    gap: 10px;
  }
  
  @keyframes fula-spin {
    to { transform: rotate(360deg); }
  }
  
  /* Responsive design */
  @media (max-width: 768px) {
    .fula-connect-section {
      flex-direction: column;
      align-items: flex-start;
    }
    
    .fula-connect-button {
      margin-top: 10px;
      width: 100%;
    }
    
    .fula-staking-options {
      flex-direction: column;
    }
    
    .fula-staking-option {
      width: 100%;
    }
    
    .fula-stats-container {
      flex-direction: column;
      gap: 10px;
    }
    
    .fula-referrer-stats-grid {
      grid-template-columns: 1fr 1fr;
    }
    
    .fula-tabs {
      flex-wrap: wrap;
    }
    
    .fula-tab {
      flex: 1;
      min-width: 80px;
      padding: 10px 5px;
      font-size: 14px;
    }
    
    .fula-button-group {
      flex-direction: column;
      gap: 5px;
    }
    
    .fula-stake-item .unstake-button, .fula-stake-item .claim-button {
      width: 100%;
      margin-right: 0;
    }
  }
  
  .fula-hint-message { min-height: 18px; transition: color 0.2s; }
  
  /* Stylish blockchain time display */
  #fula-blocktime {
    position: absolute;
    top: 10px;
    right: 20px;
    font-size: 12px;
    color: #888;
    background: rgba(255, 255, 255, 0.85);
    padding: 2px 8px;
    border-radius: 8px;
    z-index: 1000;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    font-family: monospace;
  }
</style>

<!-- HTML Structure -->
<div id="fula-staking-widget">
  <div id="fula-blocktime"></div>
  <div class="fula-header">
    <h2>$FULA Token Staking v2</h2>
    <p>Stake your $FULA tokens on Functionland Network and earn rewards</p>
  </div>
  
  <!-- Stats boxes -->
  <div class="fula-stats-container">
    <div class="fula-stats-box">
      <div class="fula-stats-title">Total FULA Staked</div>
      <div class="fula-stats-value" id="fula-total-staked">Loading...</div>
    </div>
    <div class="fula-stats-box">
      <div class="fula-stats-title">Total Stakers</div>
      <div class="fula-stats-value" id="fula-total-stakers">Loading...</div>
    </div>
    <div class="fula-stats-box">
      <div class="fula-stats-title">Total Referrers</div>
      <div class="fula-stats-value" id="fula-total-referrers">Loading...</div>
    </div>
    <div class="fula-stats-box">
      <div class="fula-stats-title">Total Staked on mining</div>
      <div class="fula-stats-value" id="fula-total-staked-mining">3M FULA</div>
    </div>
    <div class="fula-stats-box">
      <div class="fula-stats-title">Total Stakers on mining</div>
      <div class="fula-stats-value" id="fula-total-stakers-mining">100</div>
    </div>
    <div class="fula-stats-box">
      <div class="fula-stats-title">Your Total Staked</div>
      <div class="fula-stats-value" id="fula-user-staked">N/A</div>
    </div>
  </div>

  <div class="fula-connect-section">
    <div class="fula-network-selector">
      <label for="fula-network">Network:</label>
      <select id="fula-network">
        <option value="base">Base</option>
        <option value="iotex" disabled>IoTEx (coming back in 2 days)</option>
        <option value="skale" disabled>SKALE (coming back in 2 days)</option>
      </select>
      <span id="fula-contract-address" style="margin-left: 16px; font-size: 12px; color: #888;"></span>
    </div>
    <button id="fula-connect-wallet" class="fula-connect-button">Connect Wallet</button>
  </div>

  <div id="fula-wallet-info" class="fula-wallet-info">
    <div class="fula-wallet-address" id="fula-address"></div>
    <div class="fula-balance">Balance: <span id="fula-token-balance">0</span> FULA</div>
    <div class="fula-balance"><span id="fula-gas-label">ETH for Gas:</span> <span id="fula-eth-balance">0</span></div>
  </div>

  <div class="fula-tabs">
    <div class="fula-tab active" data-tab="stake">Stake</div>
    <div class="fula-tab" data-tab="unstake">Unstake</div>
    <div class="fula-tab" data-tab="referrer">Referrer</div>
    <div class="fula-tab" data-tab="info">Info</div>
  </div>

  <div id="fula-stake-tab" class="fula-tab-content active">
    <div class="fula-staking-options">
      <div class="fula-staking-option" data-period="90" data-apy="2">
        <h4>Short Term</h4>
        <div class="apy">2% APY</div>
        <div class="period">90 days lock period</div>
      </div>
      <div class="fula-staking-option" data-period="180" data-apy="6">
        <h4>Medium Term</h4>
        <div class="apy">6% APY</div>
        <div class="period">180 days lock period</div>
      </div>
      <div class="fula-staking-option" data-period="365" data-apy="15">
        <h4>Long Term</h4>
        <div class="apy">15% APY</div>
        <div class="period">365 days lock period</div>
      </div>
    </div>

    <div class="fula-input-group">
      <label for="fula-stake-amount">Amount to Stake</label>
      <div class="fula-input-wrapper">
        <input type="number" id="fula-stake-amount" placeholder="0.0" min="0" step="0.01">
        <button class="fula-max-button" id="fula-max-stake">MAX</button>
      </div>
    </div>

    <div class="fula-input-group fula-referral-input">
      <label for="fula-referrer">Referrer Address (Optional)</label>
      <div class="fula-input-wrapper">
        <input type="text" id="fula-referrer" placeholder="0x...">
      </div>
    </div>

    <div class="fula-hint-message" id="fula-stake-hint"></div>
    <button id="fula-stake-button" class="fula-action-button" disabled>Stake FULA</button>
  </div>

  <div id="fula-unstake-tab" class="fula-tab-content">
    <div class="fula-stakes-list" id="fula-stakes-container">
      <p id="fula-no-stakes-message">You don't have any active stakes.</p>
    </div>
  </div>

  <div id="fula-referrer-tab" class="fula-tab-content">
    <div class="fula-referrer-stats">
      <h3>Your Referrer Stats</h3>
      <div class="fula-referrer-stats-grid">
        <div class="fula-referrer-stat-item">
          <div class="fula-referrer-stat-label">Total Referred</div>
          <div class="fula-referrer-stat-value" id="fula-total-referred">0 FULA</div>
        </div>
        <div class="fula-referrer-stat-item">
          <div class="fula-referrer-stat-label">Total Rewards</div>
          <div class="fula-referrer-stat-value" id="fula-total-referrer-rewards">0 FULA</div>
        </div>
        <div class="fula-referrer-stat-item">
          <div class="fula-referrer-stat-label">Unclaimed Rewards</div>
          <div class="fula-referrer-stat-value" id="fula-unclaimed-rewards">0 FULA</div>
        </div>
        <div class="fula-referrer-stat-item">
          <div class="fula-referrer-stat-label">Referred Stakers</div>
          <div class="fula-referrer-stat-value" id="fula-referred-stakers-count">0</div>
        </div>
        <div class="fula-referrer-stat-item">
          <div class="fula-referrer-stat-label">Active Referred Stakers</div>
          <div class="fula-referrer-stat-value" id="fula-active-referred-stakers">0</div>
        </div>
        <div class="fula-referrer-stat-item">
          <div class="fula-referrer-stat-label">Total Active Staked</div>
          <div class="fula-referrer-stat-value" id="fula-total-active-staked">0 FULA</div>
        </div>
      </div>
      <div id="fula-referrer-rewards-summary-info"></div>
    </div>

    <h3>Your Referrer Link</h3>
    <div class="fula-input-group">
      <div class="fula-input-wrapper">
        <input type="text" id="fula-referrer-link" readonly>
        <button class="fula-max-button" id="fula-copy-link">Copy</button>
      </div>
    </div>

    <h3>Your Referrer Rewards</h3>
    <div class="fula-referrer-rewards-list" id="fula-referrer-rewards-container">
      <p id="fula-no-referrer-rewards-message">You don't have any referrer rewards yet.</p>
    </div>
  </div>

  <div id="fula-info-tab" class="fula-tab-content">
    <div class="fula-info-section">
      <h3>Staking Rewards</h3>
      <table class="fula-info-table">
        <thead>
          <tr>
            <th>Staking Period</th>
            <th>Lock Duration</th>
            <th>APY</th>
            <th>Referrer Reward</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Short Term</td>
            <td>90 days</td>
            <td>2%</td>
            <td>0%</td>
          </tr>
          <tr>
            <td>Medium Term</td>
            <td>180 days</td>
            <td>6%</td>
            <td>1%</td>
          </tr>
          <tr>
            <td>Long Term</td>
            <td>365 days</td>
            <td>15%</td>
            <td>4%</td>
          </tr>
        </tbody>
      </table>

      <h3>Important Notes</h3>
      <ul>
        <li>You cannot unstake before the lock period is over</li>
        <li>You can claim your rewards at any time</li>
        <li>Referrers can claim their rewards at any time</li>
        <li>Referrer rewards are distributed linearly over the lock period</li>
      </ul>
    </div>
  </div>

  <div id="fula-notification" class="fula-notification"></div>
</div>

<!-- Web3.js library -->
<script src="https://cdn.jsdelivr.net/npm/web3@1.8.0/dist/web3.min.js"></script>

<!-- WalletConnect Provider -->
<script src="https://cdn.jsdelivr.net/npm/@walletconnect/web3-provider@1.8.0/dist/umd/index.min.js"></script>

<!-- JavaScript Code -->
<script>
  // Helper functions
  function formatAddress(address) {
    return address.substring(0, 6) + '...' + address.substring(address.length - 4);
  }

  function formatDate(date) {
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  // Wait for DOM to be fully loaded
  document.addEventListener('DOMContentLoaded', function() {
    // Check for referrer parameter in URL and auto-fill referrer field
    const referrerParam = getURLParameter('referrer');
    if (referrerParam) {
      const referrerInput = document.getElementById('fula-referrer');
      if (referrerInput) {
        referrerInput.value = referrerParam;
      }
    }

    // Setup copy button for referrer link
    setupCopyButton();

    // Minimal ERC20 ABI for token contract
    const tokenABI = [
      {
        "constant":true,
        "inputs":[{"name":"account","type":"address"}],
        "name":"balanceOf",
        "outputs":[{"name":"","type":"uint256"}],
        "payable":false,
        "stateMutability":"view",
        "type":"function"
      },
      {
        "constant":true,
        "inputs":[],
        "name":"decimals",
        "outputs":[{"name":"","type":"uint8"}],
        "payable":false,
        "stateMutability":"view",
        "type":"function"
      },
      {
        "constant":true,
        "inputs":[{"name":"owner","type":"address"},{"name":"spender","type":"address"}],
        "name":"allowance",
        "outputs":[{"name":"","type":"uint256"}],
        "payable":false,
        "stateMutability":"view",
        "type":"function"
      },
      {
        "constant":false,
        "inputs":[{"name":"spender","type":"address"},{"name":"amount","type":"uint256"}],
        "name":"approve",
        "outputs":[{"name":"","type":"bool"}],
        "payable":false,
        "stateMutability":"nonpayable",
        "type":"function"
      }
    ];
    
    // Contract addresses for different networks
    const contractAddresses = {
      base: '0x32A2b049b1E7A6c8C26284DE49e7F05A00466a5d',
      iotex: '0xfe3574Fc1CC7c389fd916e891A497A4D986a8268',
      skale: '0xA002a09Fb3b9E8ac930B72C61De6F3979335bFa2',
      hardhat: '0xA51c1fc2f0D1a1b8494Ed1FE312d7C3a78Ed91C0' // Hardhat
    };

    // Token contract addresses for different networks
    const tokenAddresses = {
      base: '0x9e12735d77c72c5C3670636D428f2F3815d8A4cB',
      iotex: '0x9e12735d77c72c5C3670636D428f2F3815d8A4cB',
      skale: '0x9e12735d77c72c5C3670636D428f2F3815d8A4cB',
      hardhat: '0x5FbDB2315678afecb367f032d93F642f64180aa3' // Local Hardhat
    };

    // Contract ABI (updated to match StakingEngineLinear)
    const contractABI = [
      // Read functions
      {
        "inputs": [],
        "name": "getTotalStaked",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function"
      },
      {
        "inputs": [{"internalType": "address", "name": "user", "type": "address"}],
        "name": "getUserTotalStaked",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function"
      },
      {
        "inputs": [{"internalType": "address", "name": "user", "type": "address"}],
        "name": "getUserStakes",
        "outputs": [{
          "components": [
            {"internalType": "uint256", "name": "amount", "type": "uint256"},
            {"internalType": "uint256", "name": "rewardDebt", "type": "uint256"},
            {"internalType": "uint256", "name": "lockPeriod", "type": "uint256"},
            {"internalType": "uint256", "name": "startTime", "type": "uint256"},
            {"internalType": "address", "name": "referrer", "type": "address"},
            {"internalType": "bool", "name": "isActive", "type": "bool"}
          ],
          "internalType": "struct StakingEngineLinear.StakeInfo[]",
          "name": "",
          "type": "tuple[]"
        }],
        "stateMutability": "view",
        "type": "function"
      },
      {
        "inputs": [{"internalType": "address", "name": "referrer", "type": "address"}],
        "name": "getReferrerStats",
        "outputs": [{
          "components": [
            {"internalType": "uint256", "name": "totalReferred", "type": "uint256"},
            {"internalType": "uint256", "name": "totalReferrerRewards", "type": "uint256"},
            {"internalType": "uint256", "name": "unclaimedRewards", "type": "uint256"},
            {"internalType": "uint256", "name": "lastClaimTime", "type": "uint256"},
            {"internalType": "uint256", "name": "referredStakersCount", "type": "uint256"},
            {"internalType": "uint256", "name": "activeReferredStakersCount", "type": "uint256"},
            {"internalType": "uint256", "name": "totalActiveStaked", "type": "uint256"},
            {"internalType": "uint256", "name": "totalUnstaked", "type": "uint256"},
            {"internalType": "uint256", "name": "totalActiveStaked90Days", "type": "uint256"},
            {"internalType": "uint256", "name": "totalActiveStaked180Days", "type": "uint256"},
            {"internalType": "uint256", "name": "totalActiveStaked365Days", "type": "uint256"}
          ],
          "internalType": "struct StakingEngineLinear.ReferrerInfo",
          "name": "",
          "type": "tuple"
        }],
        "stateMutability": "view",
        "type": "function"
      },
      {
        "inputs": [{"internalType": "address", "name": "referrer", "type": "address"}],
        "name": "getReferrerRewards",
        "outputs": [{
          "components": [
            {"internalType": "uint256", "name": "stakeId", "type": "uint256"},
            {"internalType": "uint256", "name": "amount", "type": "uint256"},
            {"internalType": "uint256", "name": "lockPeriod", "type": "uint256"},
            {"internalType": "uint256", "name": "startTime", "type": "uint256"},
            {"internalType": "uint256", "name": "endTime", "type": "uint256"},
            {"internalType": "uint256", "name": "totalReward", "type": "uint256"},
            {"internalType": "uint256", "name": "claimedReward", "type": "uint256"},
            {"internalType": "uint256", "name": "nextClaimTime", "type": "uint256"},
            {"internalType": "bool", "name": "isActive", "type": "bool"},
            {"internalType": "address", "name": "referee", "type": "address"}
          ],
          "internalType": "struct StakingEngineLinear.ReferrerRewardInfo[]",
          "name": "",
          "type": "tuple[]"
        }],
        "stateMutability": "view",
        "type": "function"
      },
      {
        "inputs": [{"internalType": "address", "name": "referrer", "type": "address"}],
        "name": "getClaimableReferrerRewards",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function"
      },
      {
        "inputs": [],
        "name": "getAllStakerAddresses",
        "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}],
        "stateMutability": "view",
        "type": "function"
      },
      {
        "inputs": [],
        "name": "getAllReferrerAddresses",
        "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}],
        "stateMutability": "view",
        "type": "function"
      },
      // Add ABI entry for getReferrerRewardByIndex
      {
        "inputs": [
          {"internalType": "address", "name": "referrer", "type": "address"},
          {"internalType": "uint256", "name": "index", "type": "uint256"}
        ],
        "name": "getReferrerRewardByIndex",
        "outputs": [
          {
            "components": [
              {"internalType": "uint256", "name": "stakeId", "type": "uint256"},
              {"internalType": "uint256", "name": "amount", "type": "uint256"},
              {"internalType": "uint256", "name": "lockPeriod", "type": "uint256"},
              {"internalType": "uint256", "name": "startTime", "type": "uint256"},
              {"internalType": "uint256", "name": "endTime", "type": "uint256"},
              {"internalType": "uint256", "name": "totalReward", "type": "uint256"},
              {"internalType": "uint256", "name": "claimedReward", "type": "uint256"},
              {"internalType": "uint256", "name": "nextClaimTime", "type": "uint256"},
              {"internalType": "bool", "name": "isActive", "type": "bool"},
              {"internalType": "address", "name": "referee", "type": "address"}
            ],
            "internalType": "struct StakingEngineLinear.ReferrerRewardInfo",
            "name": "",
            "type": "tuple"
          }
        ],
        "stateMutability": "view",
        "type": "function"
      },
      // ABI entry for getClaimableStakerReward
      {
        "inputs": [
          {"internalType": "address", "name": "staker", "type": "address"},
          {"internalType": "uint256", "name": "stakeIndex", "type": "uint256"}
        ],
        "name": "getClaimableStakerReward",
        "outputs": [
          {"internalType": "uint256", "name": "toClaim", "type": "uint256"}
        ],
        "stateMutability": "view",
        "type": "function"
      },
      // Write functions
      {
        "inputs": [
          {"internalType": "uint256", "name": "amount", "type": "uint256"},
          {"internalType": "uint256", "name": "lockPeriod", "type": "uint256"}
        ],
        "name": "stakeToken",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function"
      },
      {
        "inputs": [
          {"internalType": "uint256", "name": "amount", "type": "uint256"},
          {"internalType": "uint256", "name": "lockPeriod", "type": "uint256"},
          {"internalType": "address", "name": "referrer", "type": "address"}
        ],
        "name": "stakeTokenWithReferrer",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function"
      },
      {
        "inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}],
        "name": "unstakeToken",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function"
      },
      {
        "inputs": [{"internalType": "uint256", "name": "stakeIndex", "type": "uint256"}],
        "name": "claimStakerReward",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function"
      },
      {
        "inputs": [{"internalType": "uint256", "name": "rewardIndex", "type": "uint256"}],
        "name": "claimReferrerReward",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function"
      },
      // ERC20
      {
        "inputs": [{"internalType": "address", "name": "account", "type": "address"}],
        "name": "balanceOf",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "payable": false,
        "stateMutability": "view",
        "type": "function"
      },
      {
        "inputs": [
          {"internalType": "address", "name": "owner", "type": "address"},
          {"internalType": "address", "name": "spender", "type": "address"}
        ],
        "name": "allowance",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "payable": false,
        "stateMutability": "view",
        "type": "function"
      },
      {
        "inputs": [
          {"internalType": "address", "name": "spender", "type": "address"},
          {"internalType": "uint256", "name": "amount", "type": "uint256"}
        ],
        "name": "approve",
        "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
        "payable": false,
        "stateMutability": "nonpayable",
        "type": "function"
      }
    ];

    // Lock periods in seconds
    const lockPeriods = {
      90: 90 * 24 * 60 * 60,  // 90 days in seconds
      180: 180 * 24 * 60 * 60, // 180 days in seconds
      365: 365 * 24 * 60 * 60  // 365 days in seconds
    };

    // State variables
    let web3;
    let currentWeb3Provider = null; // To store the active provider (Metamask or WalletConnect)
    let walletConnectProviderInstance = null; // To store WalletConnect provider instance specifically
    let stakingContract;
    let tokenContract;
    let selectedAccount;
    let selectedNetwork = 'base'; // Default to base network
    let selectedPeriod = null;
    let userStakes = [];
    let referrerRewards = [];
    let tokenBalance = 0;
    let tokenDecimals = 18;

    // Native gas token symbol mapping
    const gasSymbols = {
      hardhat: 'ETH',
      base: 'ETH',
      iotex: 'IOTX',
      skale: 'SKALE'
    };

    // Default gas prices for different networks (in Gwei)
    const defaultGasPrices = {
      base: '1.5',
      iotex: '1',
      skale: '0.1',
      hardhat: '1'
    };

    // DOM elements
    const connectButton = document.getElementById('fula-connect-wallet');
    const networkSelector = document.getElementById('fula-network');
    const walletInfo = document.getElementById('fula-wallet-info');
    const addressElement = document.getElementById('fula-address');
    const balanceElement = document.getElementById('fula-token-balance');
    const ethBalanceElement = document.getElementById('fula-eth-balance');
    const totalStakedElement = document.getElementById('fula-total-staked');
    const totalStakersElement = document.getElementById('fula-total-stakers');
    const totalReferrersElement = document.getElementById('fula-total-referrers');
    const userStakedElement = document.getElementById('fula-user-staked');
    const stakeOptions = document.querySelectorAll('.fula-staking-option');
    const stakeAmountInput = document.getElementById('fula-stake-amount');
    const maxStakeButton = document.getElementById('fula-max-stake');
    const referrerInput = document.getElementById('fula-referrer');
    const stakeButton = document.getElementById('fula-stake-button');
    const stakesContainer = document.getElementById('fula-stakes-container');
    const noStakesMessage = document.getElementById('fula-no-stakes-message');
    const tabs = document.querySelectorAll('.fula-tab');
    const tabContents = document.querySelectorAll('.fula-tab-content');
    const notification = document.getElementById('fula-notification');
    const contractAddressSpan = document.getElementById('fula-contract-address');
    const stakeHint = document.getElementById('fula-stake-hint');
    const gasLabel = document.getElementById('fula-gas-label');
    const blockTimeElement = document.getElementById('fula-blocktime');
    
    // Referrer elements
    const totalReferredElement = document.getElementById('fula-total-referred');
    const totalReferrerRewardsElement = document.getElementById('fula-total-referrer-rewards');
    const unclaimedRewardsElement = document.getElementById('fula-unclaimed-rewards');
    const referredStakersCountElement = document.getElementById('fula-referred-stakers-count');
    const activeReferredStakersElement = document.getElementById('fula-active-referred-stakers');
    const totalActiveStakedElement = document.getElementById('fula-total-active-staked');
    const referrerRewardsSummaryElement = document.getElementById('fula-referrer-rewards-summary-info');
    const referrerRewardsContainer = document.getElementById('fula-referrer-rewards-container');
    const noReferrerRewardsMessage = document.getElementById('fula-no-referrer-rewards-message');

    // Initialize
    init();

    function init() {
      // Load saved network from localStorage
      const savedNetwork = localStorage.getItem('fula-network-selected');
      if (savedNetwork && Array.from(networkSelector.options).some(opt => opt.value === savedNetwork)) {
        networkSelector.value = savedNetwork;
        selectedNetwork = savedNetwork;
      }

      // Set up event listeners
      connectButton.addEventListener('click', () => {
        if (selectedAccount) {
          disconnectWallet();
        } else {
          connectWallet();
        }
      });
      
      networkSelector.addEventListener('change', async () => {
        // Save selected network to localStorage
        localStorage.setItem('fula-network-selected', networkSelector.value);
        selectedNetwork = networkSelector.value;
        
        await switchNetwork(selectedNetwork); // Ask wallet to switch
        await waitForNetwork(selectedNetwork); // Wait for network to actually switch
        await handleNetworkChange(true); // Pass flag for user-initiated
      });
      
      maxStakeButton.addEventListener('click', setMaxAmount);
      stakeButton.addEventListener('click', handleStake);
      
      stakeOptions.forEach(option => {
        option.addEventListener('click', () => selectStakingOption(option));
      });
      
      // Add tab switching event listeners directly to each tab element
      document.querySelectorAll('.fula-tab').forEach(tab => {
        tab.addEventListener('click', function() {
          const tabName = this.getAttribute('data-tab');
          console.log('Tab clicked:', tabName);
          switchTab(tabName);
        });
      });
      
      stakeAmountInput.addEventListener('input', validateStakeAmount);
      
      // Check if Web3 is already injected
      checkWeb3Availability();
    }

    // Function to extract URL parameters
    function getURLParameter(name) {
      const urlParams = new URLSearchParams(window.location.search);
      return urlParams.get(name);
    }

    // Function to update referrer link with connected wallet address
    function updateReferrerLink() {
      console.log("updating referral link");
      const referrerLinkInput = document.getElementById('fula-referrer-link');
      if (referrerLinkInput && selectedAccount) {
        referrerLinkInput.value = `https://fx.land/fula-staking?referrer=${selectedAccount}`;
      }
    }

    // Function to handle copy button click
    function setupCopyButton()  {
      const copyButton = document.getElementById('fula-copy-link');
      if (copyButton) {
        copyButton.addEventListener('click', function() {
          const referrerLinkInput = document.getElementById('fula-referrer-link');
          if (referrerLinkInput) {
            referrerLinkInput.select();
            document.execCommand('copy');
            showNotification('Referrer link copied to clipboard!', 'success');
          }
        });
      }
    }

    function checkWeb3Availability() {
      if (window.ethereum) {
        web3 = new Web3(window.ethereum);
        
        // Listen for account changes
        window.ethereum.on('accountsChanged', handleAccountsChanged);
        
        // Listen for network changes
        window.ethereum.on('chainChanged', async (chainId) => {
          // Gracefully handle network change without reloading
          await handleNetworkChange(false);
        });
        
        // Auto-connect if previously connected
        if (localStorage.getItem('walletConnected') === 'true') {
          connectWallet();
        }
      } else {
        showNotification('No Ethereum wallet detected. Please install MetaMask or another Web3 wallet.', 'error');
      }
    }

    async function loadGlobalStats() {
      try {
        // Initialize web3 with a public provider if not already initialized
        if (!web3) {
          // Use a public RPC endpoint for the selected network
          const publicRpcUrls = {
            base: 'https://mainnet.base.org',
            iotex: 'https://babel-api.mainnet.iotex.io',
            skale: 'https://mainnet.skalenodes.com/v1/elated-tan-skat'
          };
          
          web3 = new Web3(new Web3.providers.HttpProvider(publicRpcUrls[selectedNetwork]));
        }
        
        const contractAddress = contractAddresses[selectedNetwork];
        
        // Check contract address validity before making contract calls
        if (!contractAddress || contractAddress === '******************************************') {
          console.error('Invalid contract address for network:', selectedNetwork);
          totalStakedElement.textContent = 'Contract not deployed';
          totalStakersElement.textContent = 'N/A';
          totalReferrersElement.textContent = 'N/A';
          return;
        }
        
        // Create a new contract instance for this specific call
        // This ensures we're using the correct ABI and address for the current network
        const contract = new web3.eth.Contract(contractABI, contractAddress);
        
        // Get total staked amount
        const totalStaked = await contract.methods.getTotalStaked().call();
        const totalStakedFormatted = web3.utils.fromWei(totalStaked, 'ether');
        totalStakedElement.textContent = parseFloat(totalStakedFormatted).toLocaleString() + ' FULA';
        
        // Get total stakers count
        try {
          const allStakers = await contract.methods.getAllStakerAddresses().call();
          totalStakersElement.textContent = allStakers.length.toLocaleString();
        } catch (error) {
          console.error('Error loading total stakers:', error);
          totalStakersElement.textContent = 'N/A';
        }
        
        // Get total referrers count
        try {
          const allReferrers = await contract.methods.getAllReferrerAddresses().call();
          totalReferrersElement.textContent = allReferrers.length.toLocaleString();
        } catch (error) {
          console.error('Error loading total referrers:', error);
          totalReferrersElement.textContent = 'N/A';
        }
        
        // Update contract address display
        updateContractAddressDisplay();
      } catch (error) {
        console.error('Error loading global stats:', error);
        totalStakedElement.textContent = 'Error loading data';
        totalStakersElement.textContent = 'Error loading data';
        totalReferrersElement.textContent = 'Error loading data';
      }
    }

    async function connectWallet() {
      if (!window.ethereum) {
        showNotification('No Ethereum wallet detected. Please install MetaMask or another Web3 wallet.', 'error');
        return;
      }
      
      try {
        connectButton.disabled = true;
        connectButton.innerHTML = '<span class="fula-loading"></span> Connecting...';
        
        const accounts = await window.ethereum.request({ method: 'eth_requestAccounts' });
        handleAccountsChanged(accounts);
        
        // Switch to the selected network
        await switchNetwork(selectedNetwork);
        
        localStorage.setItem('walletConnected', 'true');
        connectButton.textContent = 'Disconnect Wallet';
        connectButton.disabled = false;
        connectButton.classList.add('connected');
        updateBlockTime();
        updateReferrerLink();
      } catch (error) {
        console.error('Error connecting wallet:', error);
        showNotification('Failed to connect wallet: ' + (error.message || 'Unknown error'), 'error');
        
        connectButton.disabled = false;
        connectButton.textContent = 'Connect Wallet';
        connectButton.classList.remove('connected');
      }
    }

    async function handleAccountsChanged(accounts) {
      if (accounts.length === 0) {
        // User disconnected their wallet
        disconnectWallet();
        return;
      }
      
      selectedAccount = accounts[0];
      addressElement.textContent = formatAddress(selectedAccount);
      walletInfo.style.display = 'block';
      
      connectButton.textContent = 'Disconnect Wallet';
      connectButton.disabled = false;
      connectButton.classList.add('connected');
      
      // Initialize contracts and load data
      initializeContracts();
      await loadUserData();
    }

    function disconnectWallet() {
      selectedAccount = null;
      walletInfo.style.display = 'none';
      connectButton.textContent = 'Connect Wallet';
      connectButton.disabled = false;
      connectButton.classList.remove('connected');
      stakeButton.disabled = true;
      userStakedElement.textContent = 'N/A';
      localStorage.removeItem('walletConnected');
    }

    async function handleNetworkChange(userInitiated = false) {
      console.log("handleNetworkChange");
      selectedNetwork = networkSelector.value;
      
      // Reload global stats for the new network
      await loadGlobalStats();
      
      if (selectedAccount) {
        try {
          if (userInitiated) {
            await switchNetwork(selectedNetwork);
            await waitForNetwork(selectedNetwork); // Wait for network to actually switch
          }
          initializeContracts();
          await loadUserData();
        } catch (error) {
          console.error('Error switching network:', error);
          showNotification('Failed to switch network: ' + (error.message || 'Unknown error'), 'error');
        }
      }
      updateBlockTime();
    }

    async function switchNetwork(networkName) {
      if (!window.ethereum) return;
      
      const networkParams = {
        base: {
          chainId: '0x2105', // 8453 in hex
          chainName: 'Base Mainnet',
          nativeCurrency: { name: 'ETH', symbol: 'ETH', decimals: 18 },
          rpcUrls: ['https://mainnet.base.org'],
          blockExplorerUrls: ['https://basescan.org']
        },
        iotex: {
          chainId: '0x1251', // 4689 in hex
          chainName: 'IoTeX Mainnet',
          nativeCurrency: { name: 'IOTX', symbol: 'IOTX', decimals: 18 },
          rpcUrls: ['https://babel-api.mainnet.iotex.io'],
          blockExplorerUrls: ['https://iotexscan.io']
        },
        skale: {
          chainId: '0x79f99296', // 1351057110 in hex
          chainName: 'SKALE Network',
          nativeCurrency: { name: 'SKALE', symbol: 'SKL', decimals: 18 },
          rpcUrls: ['https://mainnet.skalenodes.com/v1/elated-tan-skat'],
          blockExplorerUrls: ['https://elated-tan-skat.explorer.mainnet.skalenodes.com']
        },
        hardhat: {
          chainId: '0x7A69', // 31337 in hex
          chainName: 'Hardhat Local Network',
          nativeCurrency: { name: 'Ether', symbol: 'ETH', decimals: 18 },
          rpcUrls: ['http://127.0.0.1:8545'],
          blockExplorerUrls: []
        }
      };
      
      const params = networkParams[networkName];
      
      try {
        // Try to switch to the network
        await window.ethereum.request({
          method: 'wallet_switchEthereumChain',
          params: [{ chainId: params.chainId }]
        });
      } catch (error) {
        // If the network is not added to MetaMask, add it
        if (error.code === 4902) {
          await window.ethereum.request({
            method: 'wallet_addEthereumChain',
            params: [params]
          });
        } else {
          throw error;
        }
      }
    }

    async function waitForNetwork(targetNetwork) {
      // Map your network names to chainIds (as lowercase hex strings with 0x prefix)
      const chainIds = {
        base: '0x2105',
        iotex: '0x1251',
        skale: '0x79f99296',
        hardhat: '0x7a69'
      };
      const targetChainId = chainIds[targetNetwork]?.toLowerCase();
      if (!targetChainId) {
        console.error('Unknown network:', targetNetwork);
        return;
      }
      
      if (!window.ethereum) {
        console.error('No ethereum provider available');
        return;
      }
      
      let currentChainId = await window.ethereum.request({ method: 'eth_chainId' });
      currentChainId = currentChainId.toLowerCase();
      
      if (currentChainId === targetChainId) return; // Already on correct network
      
      // Prevent multiple simultaneous requests
      if (window.fulaNetworkSwitchPending) {
        showNotification('Please approve or reject the pending network switch in MetaMask.', 'error');
        throw new Error('Network switch already pending. Please approve or reject in MetaMask.');
      }
      
      window.fulaNetworkSwitchPending = true;
      try {
        await window.ethereum.request({
          method: 'wallet_switchEthereumChain',
          params: [{ chainId: targetChainId }]
        });
      } catch (switchError) {
        if (switchError.code === 4001) {
          throw new Error('Please approve the network switch in MetaMask.');
        } else if (switchError.message && switchError.message.includes('already pending')) {
          showNotification('Please approve or reject the pending network switch in MetaMask.', 'error');
          throw new Error('Network switch already pending. Please approve or reject in MetaMask.');
        } else {
          throw new Error('Network switch failed: ' + (switchError.message || switchError));
        }
      } finally {
        window.fulaNetworkSwitchPending = false;
      }
      
      // Wait for the network to actually switch
      for (let i = 0; i < 30; i++) {
        await new Promise(res => setTimeout(res, 100));
        currentChainId = await window.ethereum.request({ method: 'eth_chainId' });
        currentChainId = currentChainId.toLowerCase();
        if (currentChainId === targetChainId) return;
      }
      
      console.error('waitForNetwork timeout: wanted', targetChainId, 'got', currentChainId);
      throw new Error('Network did not switch in time. Please check MetaMask and try again.');
    }

    function initializeContracts() {
      try {
        if (!web3 || !selectedNetwork) {
          console.error('Web3 or selectedNetwork not available');
          return;
        }
        
        const contractAddress = contractAddresses[selectedNetwork];
        
        // Check contract address validity before making contract calls
        if (!contractAddress || contractAddress === '******************************************') {
          console.error('Invalid contract address for network:', selectedNetwork);
          return;
        }
        
        // Create new contract instances with the current web3 provider and network
        stakingContract = new web3.eth.Contract(contractABI, contractAddress);
        
        // Always use the actual token contract for tokenContract
        const tokenAddress = tokenAddresses[selectedNetwork];
        if (tokenAddress) {
          tokenContract = new web3.eth.Contract(tokenABI, tokenAddress);
        } else {
          tokenContract = null;
          console.error('No token address for network:', selectedNetwork);
        }
      } catch (error) {
        console.error('Error initializing contracts:', error);
        showNotification('Failed to initialize contracts: ' + (error.message || 'Unknown error'), 'error');
      }
    }

    async function loadUserData() {
      // Ensure network is switched before loading user data
      if (!selectedAccount || !stakingContract) return;
      
      try {
        // Get token balance from correct token contract
        const tokenAddress = tokenAddresses[selectedNetwork];
        if (!tokenAddress) {
          balanceElement.textContent = 'Token address not set';
        } else {
          const tokenContractInstance = new web3.eth.Contract(tokenABI, tokenAddress);
          // Try to get decimals dynamically (fallback to 18)
          let decimals = 18;
          try {
            decimals = await tokenContractInstance.methods.decimals().call();
          } catch (e) {
            console.error('Error getting token decimals:', e);
          }
          
          const balance = await tokenContractInstance.methods.balanceOf(selectedAccount).call();
          tokenBalance = balance / (10 ** decimals);
          balanceElement.textContent = parseFloat(tokenBalance).toFixed(4);
        }
        
        // Update gas label for current network
        gasLabel.textContent = (gasSymbols[selectedNetwork] || 'ETH') + ' for Gas:';
        
        // Get ETH/native balance for gas
        const ethBalanceWei = await web3.eth.getBalance(selectedAccount);
        const ethBalance = web3.utils.fromWei(ethBalanceWei, 'ether');
        ethBalanceElement.textContent = parseFloat(ethBalance).toFixed(4);
        
        // Get user's total staked amount
        try {
          const userTotalStaked = await stakingContract.methods.getUserTotalStaked(selectedAccount).call();
          const userTotalStakedFormatted = web3.utils.fromWei(userTotalStaked, 'ether');
          userStakedElement.textContent = parseFloat(userTotalStakedFormatted).toFixed(4) + ' FULA';
        } catch (error) {
          console.error('Error getting user total staked:', error);
          userStakedElement.textContent = 'Error loading data';
        }
        
        // Get user's stakes
        await loadUserStakes();
        
        // Get referrer stats
        await loadReferrerStats();
        
        // Validate stake amount in case balance changed
        validateStakeAmount();
      } catch (error) {
        console.error('Error loading user data:', error);
        showNotification('Failed to load user data: ' + (error.message || 'Unknown error'), 'error');
      }
    }

    async function loadUserStakes() {
      try {
        const stakes = await stakingContract.methods.getUserStakes(selectedAccount).call();
        userStakes = stakes;
        
        // Clear stakes container
        stakesContainer.innerHTML = '';
        
        if (stakes.length === 0 || !stakes.some(stake => stake.isActive)) {
          stakesContainer.appendChild(noStakesMessage);
          return;
        }
        
        // Hide no stakes message
        noStakesMessage.style.display = 'none';
        
        // Display active stakes
        const block = await web3.eth.getBlock('latest');
        const blockTimestamp = block.timestamp;
        stakes.forEach((stake, index) => {
          if (!stake.isActive) return;
          
          const stakeAmount = web3.utils.fromWei(stake.amount, 'ether');
          const startTime = new Date(stake.startTime * 1000);
          const endTime = new Date((parseInt(stake.startTime) + parseInt(stake.lockPeriod)) * 1000);
          const isLocked = blockTimestamp < ((parseInt(stake.startTime) + parseInt(stake.lockPeriod)));
          
          let periodLabel = '';
          let apyRate = '';
          
          if (stake.lockPeriod == lockPeriods[90]) {
            periodLabel = 'Short Term (90 days)';
            apyRate = '2%';
          } else if (stake.lockPeriod == lockPeriods[180]) {
            periodLabel = 'Medium Term (180 days)';
            apyRate = '6%';
          } else if (stake.lockPeriod == lockPeriods[365]) {
            periodLabel = 'Long Term (365 days)';
            apyRate = '15%';
          }
          
          // Calculate claimable rewards
          let claimableRewards = 0;
          let totalPossibleReward = 0;
          let rewardProgress = 0;
          
          const stakeElement = document.createElement('div');
          stakeElement.className = 'fula-stake-item';
          stakeElement.innerHTML = `
            <div class="stake-header">
              <div class="stake-amount">${parseFloat(stakeAmount).toFixed(4)} FULA</div>
              <div class="stake-period">${periodLabel} - ${apyRate} APY</div>
            </div>
            <div class="stake-details">
              <div>Start: ${formatDate(startTime)}</div>
              <div>End: ${formatDate(endTime)}</div>
            </div>
            <div class="stake-details">
              <div>Status: ${isLocked ? 'Locked' : 'Unlocked'}</div>
              <div>${isLocked ? 'Cannot unstake yet' : 'Ready to unstake'}</div>
            </div>
            <div class="stake-rewards">
              <div>Claimable Rewards:</div>
              <div class="fula-claimable-rewards">${claimableRewards.toFixed(6)} FULA</div>
            </div>
            <div class="fula-button-group">
              <button class="claim-button" data-index="${index}" ${claimableRewards > 0 ? '' : 'disabled'}>Claim Rewards</button>
              <button class="unstake-button" data-index="${index}" ${isLocked ? 'disabled' : ''}>Unstake</button>
            </div>
          `;

          (async () => {
            try {
              claimableRewards = await stakingContract.methods.getClaimableStakerReward(selectedAccount, index).call();
              console.log(`claimableRewards: ${claimableRewards} for account ${selectedAccount} at index ${index}`);
              claimableRewards = parseFloat(web3.utils.fromWei(claimableRewards, 'ether'));
              const claimableDiv = stakeElement.querySelector('.fula-claimable-rewards');
              if (claimableDiv) claimableDiv.textContent = claimableRewards.toFixed(6) + ' FULA';
              // Enable/disable claim button based on new value
              const claimBtn = stakeElement.querySelector('.claim-button');
              if (claimBtn) claimBtn.disabled = !(claimableRewards > 0);
            } catch (e) {
              console.error('Error fetching claimableRewards:', e);
              claimableRewards = 0;
            }
          })();
          
          const unstakeButton = stakeElement.querySelector('.unstake-button');
          unstakeButton.addEventListener('click', async () => {
            // Always fetch the latest claimable rewards from the contract
            let latestClaimable = 0;
            try {
              latestClaimable = await stakingContract.methods.getClaimableStakerReward(selectedAccount, index).call();
            } catch (e) {
              console.error('Error fetching claimableRewards before unstake:', e);
            }
            if (parseFloat(web3.utils.fromWei(latestClaimable, 'ether')) > 0) {
              await handleClaimStakerReward(index);
            }
            handleUnstake(index);
          });
          
          const claimButton = stakeElement.querySelector('.claim-button');
          claimButton.addEventListener('click', () => handleClaimStakerReward(index));
          
          stakesContainer.appendChild(stakeElement);
        });
      } catch (error) {
        console.error('Error loading user stakes:', error);
        stakesContainer.innerHTML = '<p>Error loading stakes. Please try again.</p>';
      }
    }

    async function loadReferrerStats() {
      try {
        // Get referrer stats
        const referrerStats = await stakingContract.methods.getReferrerStats(selectedAccount).call();
        
        // Update UI with referrer stats
        totalReferredElement.textContent = parseFloat(web3.utils.fromWei(referrerStats.totalReferred, 'ether')).toFixed(4) + ' FULA';
        totalReferrerRewardsElement.textContent = parseFloat(web3.utils.fromWei(referrerStats.totalReferrerRewards, 'ether')).toFixed(4) + ' FULA';
        unclaimedRewardsElement.textContent = parseFloat(web3.utils.fromWei(referrerStats.unclaimedRewards, 'ether')).toFixed(4) + ' FULA';
        referredStakersCountElement.textContent = referrerStats.referredStakersCount;
        activeReferredStakersElement.textContent = referrerStats.activeReferredStakersCount;
        totalActiveStakedElement.textContent = parseFloat(web3.utils.fromWei(referrerStats.totalActiveStaked, 'ether')).toFixed(4) + ' FULA';
        
        // Get referrer rewards
        await loadReferrerRewards();
      } catch (error) {
        console.error('Error loading referrer stats:', error);
        // Set default values for referrer stats
        totalReferredElement.textContent = '0 FULA';
        totalReferrerRewardsElement.textContent = '0 FULA';
        unclaimedRewardsElement.textContent = '0 FULA';
        referredStakersCountElement.textContent = '0';
        activeReferredStakersElement.textContent = '0';
        totalActiveStakedElement.textContent = '0 FULA';
      }
    }

    async function loadReferrerRewards() {
      try {
        const rewards = await stakingContract.methods.getReferrerRewards(selectedAccount).call();
        referrerRewards = rewards;
        referrerRewardsContainer.innerHTML = '';
        if (rewards.length === 0 || !rewards.some(reward => reward.isActive)) {
          referrerRewardsContainer.appendChild(noReferrerRewardsMessage);
          return;
        }
        noReferrerRewardsMessage.style.display = 'none';

        // Show total rewards summary
        let totalClaimable = 0;
        let totalUnclaimed = 0;
        let totalRewards = 0;
        // We'll fetch accurate claimable per index from the contract
        const block = await web3.eth.getBlock('latest');
        const blockTimestamp = block.timestamp;

        for (let i = 0; i < rewards.length; i++) {
          if (!rewards[i].isActive) continue;
          const rewardInfo = await stakingContract.methods.getReferrerRewardByIndex(selectedAccount, i).call();
          const rewardAmount = web3.utils.fromWei(rewardInfo.totalReward, 'ether');
          const claimedAmount = web3.utils.fromWei(rewardInfo.claimedReward, 'ether');
          const startTime = new Date(rewardInfo.startTime * 1000);
          const endTime = new Date(rewardInfo.endTime * 1000);
          const nextClaimTime = new Date(rewardInfo.nextClaimTime * 1000);
          const total = parseFloat(rewardAmount);
          const claimed = parseFloat(claimedAmount);
          const remaining = total - claimed;
          // Calculate claimable using contract logic
          const lockEnd = rewardInfo.startTime + rewardInfo.lockPeriod;
          const nowOrEnd = blockTimestamp < lockEnd ? blockTimestamp : lockEnd;
          const timeElapsed = nowOrEnd - rewardInfo.startTime;
          
          // Use BN for proper calculation without floating point issues
          const totalRewardBN = web3.utils.toBN(rewardInfo.totalReward);
          const timeElapsedBN = web3.utils.toBN(timeElapsed);
          const lockPeriodBN = web3.utils.toBN(rewardInfo.lockPeriod);
          const claimedRewardBN = web3.utils.toBN(rewardInfo.claimedReward);
          
          // Calculate: (totalReward * timeElapsed / lockPeriod) - claimedReward
          let claimableBN;
          try {
            const rewardToDateBN = totalRewardBN.mul(timeElapsedBN).div(lockPeriodBN);
            claimableBN = rewardToDateBN.sub(claimedRewardBN);
            if (claimableBN.isNeg()) {
              claimableBN = web3.utils.toBN(0);
            }
          } catch (calcError) {
            console.error('Error in BN calculation:', calcError);
            claimableBN = web3.utils.toBN(0);
          }
          
          const claimableFula = web3.utils.fromWei(claimableBN, 'ether');
          totalClaimable += parseFloat(claimableFula);
          totalUnclaimed += remaining;
          totalRewards += total;

          const rewardElement = document.createElement('div');
          rewardElement.className = 'fula-referrer-reward-item';
          rewardElement.innerHTML = `
            <div class="fula-referrer-reward-header">
              <div class="fula-referrer-reward-referee">${formatAddress(rewardInfo.referee)}</div>
              <div class="fula-referrer-reward-amount">${total.toFixed(6)} FULA</div>
            </div>
            <div class="fula-referrer-reward-details">
              <div>Claimed: ${claimed.toFixed(6)} FULA</div>
              <div>Remaining: ${remaining.toFixed(6)} FULA</div>
            </div>
            <div class="fula-referrer-reward-details">
              <div>Start: ${formatDate(startTime)}</div>
              <div>End: ${formatDate(endTime)}</div>
            </div>
            <div class="fula-referrer-reward-details">
              <div>Next Claim: ${formatDate(nextClaimTime)}</div>
              <div>Claimable: ${parseFloat(claimableFula).toFixed(6)} FULA</div>
            </div>
            <div class="fula-referrer-reward-progress">
              <div class="fula-referrer-reward-progress-bar" style="width: ${(timeElapsed / rewardInfo.lockPeriod) * 100}%"></div>
            </div>
            <button class="fula-action-button" data-index="${i}" ${claimableBN.isZero() ? 'disabled' : ''}>
              Claim Reward
            </button>
          `;
          const claimButton = rewardElement.querySelector('.fula-action-button');
          claimButton.addEventListener('click', () => handleClaimReferrerReward(i));
          referrerRewardsContainer.appendChild(rewardElement);
        }
        // Optionally show a summary at the top
        referrerRewardsSummaryElement.innerHTML = `
          <div>Total Referrer Rewards: ${totalRewards.toFixed(6)} FULA</div>
          <div>Total Unclaimed: ${totalUnclaimed.toFixed(6)} FULA</div>
          <div>Total Claimable: ${totalClaimable.toFixed(6)} FULA</div>
        `;
      } catch (error) {
        console.error('Error loading referrer rewards:', error);
        referrerRewardsContainer.innerHTML = '<p>Error loading referrer rewards. Please try again.</p>';
      }
    }

    // Helper function to estimate gas for a transaction with method-specific fallbacks
  async function estimateGasWithFallback(method, params) {
    try {
      // Try to estimate gas using the contract method
      const gasEstimate = await method.estimateGas(params);
      // Add a buffer to the gas estimate (20% more)
      return Math.ceil(gasEstimate * 1.2);
    } catch (error) {
      console.warn('Gas estimation failed, using fallback gas limit:', error);
      
      // Get method name from the method object
      const methodName = method._method ? method._method.name : '';
      
      // Use specific gas limits from Hardhat gas report for each method
      const gasLimits = {
        // Values from the Hardhat gas report with 20% buffer
        stakeToken: 458245,             // 381,871 * 1.2
        stakeTokenWithReferrer: 896182, // 746,818 * 1.2
        unstakeToken: 181097,           // 150,914 * 1.2
        claimStakerReward: 140437,      // 117,031 * 1.2
        claimReferrerReward: 136006,    // 113,338 * 1.2
        addRewardsToPool: 81320,        // 67,767 * 1.2
        emergencyPauseRewardDistribution: 59683,  // 49,736 * 1.2
        emergencyUnpauseRewardDistribution: 33365 // 27,804 * 1.2
      };
      
      // Return the specific gas limit for the method if available, otherwise use default
      return gasLimits[methodName] || 300000; // Default fallback gas limit if method not found
    }
  }

    // Helper function to get gas price with fallback
    async function getGasPriceWithFallback() {
      try {
        // Try to get current gas price from the network
        const gasPrice = await web3.eth.getGasPrice();
        // Convert to Gwei for readability
        const gasPriceGwei = web3.utils.fromWei(gasPrice, 'gwei');
        console.log('Current gas price:', gasPriceGwei, 'Gwei');
        return gasPrice;
      } catch (error) {
        console.warn('Gas price fetch failed, using fallback gas price:', error);
        // Use a fallback gas price based on the network
        const fallbackGasPriceGwei = defaultGasPrices[selectedNetwork] || '1.5';
        const fallbackGasPrice = web3.utils.toWei(fallbackGasPriceGwei, 'gwei');
        console.log('Using fallback gas price:', fallbackGasPriceGwei, 'Gwei');
        return fallbackGasPrice;
      }
    }

    async function handleStake() {
      if (!selectedAccount || !stakingContract || !tokenContract) return;
      
      const amount = parseFloat(stakeAmountInput.value);
      if (!amount || amount <= 0 || !selectedPeriod) {
        showNotification('Please enter a valid amount and select a staking period.', 'error');
        return;
      }
      
      const amountWei = web3.utils.toWei(amount.toString(), 'ether');
      const referrer = referrerInput.value.trim();
      
      try {
        stakeButton.disabled = true;
        stakeButton.innerHTML = '<span class="fula-loading"></span> Processing...';
        
        // Check allowance
        const allowance = await tokenContract.methods.allowance(selectedAccount, stakingContract.options.address).call();
        
        if (BigInt(allowance) < BigInt(amountWei)) {
          // Prepare approve transaction with explicit gas parameters
          const approveMethod = tokenContract.methods.approve(stakingContract.options.address, amountWei);
          const gasLimit = await estimateGasWithFallback(approveMethod, { from: selectedAccount });
          const gasPrice = await getGasPriceWithFallback();
          
          // Approve token transfer with explicit gas parameters
          await approveMethod.send({
            from: selectedAccount,
            gas: gasLimit,
            gasPrice: gasPrice
          });
        }
        
        // Prepare stake transaction
        let stakeMethod;
        let stakeParams = { from: selectedAccount };
        
        if (referrer && web3.utils.isAddress(referrer) && referrer !== selectedAccount) {
          stakeMethod = stakingContract.methods.stakeTokenWithReferrer(amountWei, lockPeriods[selectedPeriod], referrer);
        } else {
          stakeMethod = stakingContract.methods.stakeToken(amountWei, lockPeriods[selectedPeriod]);
        }
        
        // Estimate gas with fallback and get current gas price
        const gasLimit = await estimateGasWithFallback(stakeMethod, stakeParams);
        const gasPrice = await getGasPriceWithFallback();
        
        // Add explicit gas parameters to transaction
        stakeParams.gas = gasLimit;
        stakeParams.gasPrice = gasPrice;
        
        // Execute stake transaction with explicit gas parameters
        await stakeMethod.send(stakeParams);
        
        showNotification('Successfully staked ' + amount + ' FULA!', 'success');
        
        // Reset form
        stakeAmountInput.value = '';
        referrerInput.value = '';
        stakeOptions.forEach(opt => opt.classList.remove('selected'));
        selectedPeriod = null;
        stakeButton.disabled = true;
        
        // Reload user data
        await loadUserData();
        await loadGlobalStats();
      } catch (error) {
        console.error('Error staking tokens:', error);
        showNotification('Failed to stake tokens: ' + (error.message || 'Unknown error'), 'error');
      } finally {
        stakeButton.disabled = false;
        stakeButton.textContent = 'Stake FULA';
      }
    }

    async function handleUnstake(index) {
      if (!selectedAccount || !stakingContract) return;
      
      try {
        const unstakeButton = document.querySelector(`.unstake-button[data-index="${index}"]`);
        unstakeButton.disabled = true;
        unstakeButton.innerHTML = '<span class="fula-loading"></span> Processing...';
        
        // Prepare unstake transaction
        const unstakeMethod = stakingContract.methods.unstakeToken(index);
        
        // Estimate gas with fallback and get current gas price
        const gasLimit = await estimateGasWithFallback(unstakeMethod, { from: selectedAccount });
        const gasPrice = await getGasPriceWithFallback();
        
        // Execute unstake transaction with explicit gas parameters
        await unstakeMethod.send({
          from: selectedAccount,
          gas: gasLimit,
          gasPrice: gasPrice
        });
        
        showNotification('Successfully unstaked FULA!', 'success');
        
        // Reload user data
        await loadUserData();
        await loadGlobalStats();
      } catch (error) {
        console.error('Error unstaking tokens:', error);
        showNotification('Failed to unstake tokens: ' + (error.message || 'Unknown error'), 'error');
        
        const unstakeButton = document.querySelector(`.unstake-button[data-index="${index}"]`);
        if (unstakeButton) {
          unstakeButton.disabled = false;
          unstakeButton.textContent = 'Unstake';
        }
      }
    }

    async function handleClaimStakerReward(index) {
      if (!selectedAccount || !stakingContract) return;
      
      try {
        const claimButton = document.querySelector(`.claim-button[data-index="${index}"]`);
        claimButton.disabled = true;
        claimButton.innerHTML = '<span class="fula-loading"></span> Processing...';
        
        // Prepare claim transaction
        const claimMethod = stakingContract.methods.claimStakerReward(index);
        
        // Estimate gas with fallback and get current gas price
        const gasLimit = await estimateGasWithFallback(claimMethod, { from: selectedAccount });
        const gasPrice = await getGasPriceWithFallback();
        
        // Execute claim transaction with explicit gas parameters
        await claimMethod.send({
          from: selectedAccount,
          gas: gasLimit,
          gasPrice: gasPrice
        });
        
        showNotification('Successfully claimed staker rewards!', 'success');
        
        // Reload user data
        await loadUserData();
      } catch (error) {
        console.error('Error claiming staker rewards:', error);
        showNotification('Failed to claim staker rewards: ' + (error.message || 'Unknown error'), 'error');
        
        const claimButton = document.querySelector(`.claim-button[data-index="${index}"]`);
        if (claimButton) {
          claimButton.disabled = false;
          claimButton.textContent = 'Claim Rewards';
        }
      }
    }

    async function handleClaimReferrerReward(index) {
      if (!selectedAccount || !stakingContract) return;
      
      try {
        const claimButton = document.querySelector(`.fula-referrer-reward-item:nth-child(${index + 1}) .fula-action-button`);
        if (claimButton) {
          claimButton.disabled = true;
          claimButton.innerHTML = '<span class="fula-loading"></span> Processing...';
        }
        
        // Prepare claim transaction
        const claimMethod = stakingContract.methods.claimReferrerReward(index);
        
        // Estimate gas with fallback and get current gas price
        const gasLimit = await estimateGasWithFallback(claimMethod, { from: selectedAccount });
        const gasPrice = await getGasPriceWithFallback();
        
        // Execute claim transaction with explicit gas parameters
        await claimMethod.send({
          from: selectedAccount,
          gas: gasLimit,
          gasPrice: gasPrice
        });
        
        showNotification('Successfully claimed referrer reward!', 'success');
        
        // Reload referrer stats
        await loadReferrerStats();
      } catch (error) {
        console.error('Error claiming referrer reward:', error);
        showNotification('Failed to claim referrer reward: ' + (error.message || 'Unknown error'), 'error');
        
        const claimButton = document.querySelector(`.fula-referrer-reward-item:nth-child(${index + 1}) .fula-action-button`);
        if (claimButton) {
          claimButton.disabled = false;
          claimButton.textContent = 'Claim Reward';
        }
      }
    }

    function selectStakingOption(option) {
      // Remove selected class from all options
      stakeOptions.forEach(opt => opt.classList.remove('selected'));
      
      // Add selected class to clicked option
      option.classList.add('selected');
      
      // Store selected period
      selectedPeriod = option.dataset.period;
      
      // Validate stake amount
      validateStakeAmount();
    }

    function validateStakeAmount() {
      const amount = parseFloat(stakeAmountInput.value);
      const hint = stakeHint;
      hint.textContent = '';
      if (amount > 0 && selectedPeriod && amount <= parseFloat(tokenBalance)) {
        stakeButton.disabled = false;
        hint.textContent = '';
      } else {
        stakeButton.disabled = true;
        if (!selectedPeriod) {
          hint.textContent = 'You need to select a staking period.';
        } else if (!amount || amount <= 0) {
          hint.textContent = 'Please enter an amount greater than 0.';
        } else if (amount > parseFloat(tokenBalance)) {
          hint.textContent = 'Amount exceeds your FULA balance.';
        }
      }
    }

    function setMaxAmount() {
      stakeAmountInput.value = parseFloat(tokenBalance).toFixed(4);
      validateStakeAmount();
    }

    function switchTab(tabName) {
      // Remove active class from all tabs and tab contents
      tabs.forEach(tab => tab.classList.remove('active'));
      tabContents.forEach(content => content.classList.remove('active'));
      
      // Add active class to selected tab and tab content
      document.querySelector(`.fula-tab[data-tab="${tabName}"]`).classList.add('active');
      document.getElementById(`fula-${tabName}-tab`).classList.add('active');
      // Update referrer link when switching to referrer tab
      if (tabName === 'referrer') {
        updateReferrerLink();
      }
    }

    async function updateBlockTime() {
      try {
        if (!web3) return;
        const latestBlock = await web3.eth.getBlock('latest');
        if (latestBlock && latestBlock.timestamp) {
          const date = new Date(latestBlock.timestamp * 1000);
          blockTimeElement.textContent = 'Block Time: ' + date.toLocaleString();
        } else {
          blockTimeElement.textContent = '';
        }
      } catch (err) {
        blockTimeElement.textContent = '';
      }
    }

    setInterval(updateBlockTime, 15000);
    updateBlockTime();

    function showNotification(message, type) {
      notification.textContent = message;
      notification.className = 'fula-notification ' + type;
      notification.classList.add('show');
      
      setTimeout(() => {
        notification.classList.remove('show');
      }, 5000);
    }

    function updateContractAddressDisplay() {
      const addr = contractAddresses[networkSelector.value];
      contractAddressSpan.innerHTML = addr ? `Contract: ${addr}<br>Token: ${tokenAddresses[networkSelector.value]}` : '';
    }

    networkSelector.addEventListener('change', updateContractAddressDisplay);
    updateContractAddressDisplay();

    // Load global stats on page load
    loadGlobalStats();
  });
</script>
