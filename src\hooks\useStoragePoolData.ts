import { useState, useEffect, useCallback } from 'react'
import { useAccount, usePublicClient } from 'wagmi'
import { STORAGE_POOL_ABI } from '@/config/abis'
import { STORAGE_POOL_CONTRACT_ADDRESSES } from '@/config/contracts'
import { useChainId } from 'wagmi'

export interface PoolInfo {
  id: number
  name: string
  region: string
  requiredTokens: bigint
  minPingTime: bigint
  maxChallengeResponsePeriod: number
  creator: string
  memberCount: number
  maxMembers: number
  joinRequests: JoinRequestInfo[]
}

export interface JoinRequestInfo {
  account: string
  poolId: number
  timestamp: number
  status: number
  approvals: number
  rejections: number
  peerId: string
  index: number
}

export function useStoragePoolData() {
  const { address } = useAccount()
  const chainId = useChainId()
  const publicClient = usePublicClient()
  const [pools, setPools] = useState<PoolInfo[]>([])
  const [poolIds, setPoolIds] = useState<number[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const contractAddress = STORAGE_POOL_CONTRACT_ADDRESSES[chainId]

  // Fetch pool IDs first
  const fetchPoolIds = useCallback(async () => {
    if (!contractAddress || !publicClient) return []

    try {
      console.log('Fetching pool IDs from contract:', contractAddress)
      const ids: number[] = []
      let index = 0

      // Keep fetching until we get an error (no more pool IDs)
      while (true) {
        try {
          const poolId = await publicClient.readContract({
            address: contractAddress as `0x${string}`,
            abi: STORAGE_POOL_ABI,
            functionName: 'poolIds',
            args: [BigInt(index)],
          })
          console.log(`Pool ID at index ${index}:`, poolId)
          ids.push(Number(poolId))
          index++
        } catch (err) {
          console.log(`No more pool IDs at index ${index}`)
          break
        }
      }

      console.log('Total pool IDs found:', ids)
      return ids
    } catch (err) {
      console.error('Error fetching pool IDs:', err)
      return []
    }
  }, [contractAddress, publicClient])

  // Fetch all pool data
  const fetchPoolsData = useCallback(async () => {
    if (!contractAddress || !publicClient) {
      setPools([])
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      // First get all pool IDs
      const currentPoolIds = await fetchPoolIds()
      setPoolIds(currentPoolIds)

      if (currentPoolIds.length === 0) {
        setPools([])
        setIsLoading(false)
        return
      }

      const poolsData: PoolInfo[] = []

      // Get pool details from PoolCreated events
      // This is the proper way to get pool information since the pools mapping contains complex structs
      try {
        console.log('Fetching PoolCreated events for contract:', contractAddress)

        const poolCreatedEvents = await publicClient.getLogs({
          address: contractAddress as `0x${string}`,
          event: {
            type: 'event',
            name: 'PoolCreated',
            inputs: [
              { indexed: true, name: 'poolId', type: 'uint32' },
              { indexed: true, name: 'creator', type: 'address' },
              { indexed: false, name: 'name', type: 'string' },
              { indexed: false, name: 'region', type: 'string' },
              { indexed: false, name: 'requiredTokens', type: 'uint256' },
              { indexed: false, name: 'maxMembers', type: 'uint32' },
            ],
          },
          fromBlock: 'earliest',
          toBlock: 'latest',
        })

        console.log('Found PoolCreated events:', poolCreatedEvents.length)
        console.log('Pool IDs found:', currentPoolIds)

        // Create a map of pool data from events
        const poolEventData = new Map()
        poolCreatedEvents.forEach((event: any) => {
          if (event.args) {
            console.log('Processing pool event:', event.args)
            poolEventData.set(Number(event.args.poolId), {
              name: event.args.name,
              region: event.args.region,
              requiredTokens: event.args.requiredTokens,
              maxMembers: Number(event.args.maxMembers),
              creator: event.args.creator,
            })
          }
        })

        // Create pool data using event information
        for (const poolId of currentPoolIds) {
          const eventData = poolEventData.get(poolId)

          poolsData.push({
            id: poolId,
            name: eventData?.name || `Pool ${poolId}`,
            region: eventData?.region || 'Unknown',
            requiredTokens: eventData?.requiredTokens || BigInt(0),
            minPingTime: BigInt(0), // Not available in event, would need separate call
            maxChallengeResponsePeriod: 0, // Not available in event, would need separate call
            creator: eventData?.creator || '0x0000000000000000000000000000000000000000',
            memberCount: 0, // Would need to calculate from member events
            maxMembers: eventData?.maxMembers || 0,
            joinRequests: [], // Would need to fetch from join request events
          })
        }
      } catch (eventError) {
        console.warn('Could not fetch pool events, using basic pool data:', eventError)

        // Fallback: create basic pool entries
        for (const poolId of currentPoolIds) {
          poolsData.push({
            id: poolId,
            name: `Pool ${poolId}`,
            region: 'Unknown',
            requiredTokens: BigInt(0),
            minPingTime: BigInt(0),
            maxChallengeResponsePeriod: 0,
            creator: '0x0000000000000000000000000000000000000000',
            memberCount: 0,
            maxMembers: 0,
            joinRequests: [],
          })
        }
      }

      setPools(poolsData)
    } catch (err) {
      console.error('Error fetching pools data:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch pools data')
    } finally {
      setIsLoading(false)
    }
  }, [contractAddress, publicClient, fetchPoolIds])

  // Fetch specific pool data
  const fetchPoolData = useCallback(async (poolId: number): Promise<PoolInfo | null> => {
    if (!contractAddress) return null

    try {
      const existingPool = pools.find(p => p.id === poolId)
      if (existingPool) return existingPool

      // If not in cache, trigger a refresh
      await fetchPoolsData()
      return pools.find(p => p.id === poolId) || null
    } catch (err) {
      console.error(`Error fetching pool ${poolId} data:`, err)
      return null
    }
  }, [contractAddress, pools, fetchPoolsData])

  // Get join requests for all pools
  const getAllJoinRequests = useCallback((): JoinRequestInfo[] => {
    const allRequests: JoinRequestInfo[] = []

    pools.forEach(pool => {
      pool.joinRequests.forEach(request => {
        allRequests.push(request)
      })
    })

    return allRequests
  }, [pools])

  // Refresh all data
  const refreshData = useCallback(async () => {
    await fetchPoolsData()
  }, [fetchPoolsData])

  // Auto-fetch on mount
  useEffect(() => {
    fetchPoolsData()
  }, [fetchPoolsData])

  return {
    pools,
    poolIds,
    isLoading,
    error,
    poolCount: poolIds.length,
    fetchPoolData,
    getAllJoinRequests,
    refreshData,
  }
}

// Hook for checking if user is member of specific pool
export function usePoolMembership(poolId: number, userAddress?: string) {
  const chainId = useChainId()
  const publicClient = usePublicClient()
  const contractAddress = STORAGE_POOL_CONTRACT_ADDRESSES[chainId]
  const [isMember, setIsMember] = useState(false)
  const [hasJoinRequest, setHasJoinRequest] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const checkMembership = useCallback(async () => {
    if (!contractAddress || !userAddress || !publicClient) return

    setIsLoading(true)
    try {
      // Check if user is member of any pool using the available function
      const memberOfAnyPool = await publicClient.readContract({
        address: contractAddress as `0x${string}`,
        abi: STORAGE_POOL_ABI,
        functionName: 'isMemberOfAnyPool',
        args: [userAddress as `0x${string}`],
      })

      // For specific pool membership, we'd need to check the pool's member list
      // This is a simplified check - in practice you'd iterate through join requests
      setIsMember(!!memberOfAnyPool)
      setHasJoinRequest(false) // Would need to check join requests array
    } catch (err) {
      console.error('Error checking membership:', err)
      setIsMember(false)
      setHasJoinRequest(false)
    } finally {
      setIsLoading(false)
    }
  }, [contractAddress, userAddress, publicClient])

  useEffect(() => {
    checkMembership()
  }, [checkMembership])

  return {
    isMember,
    hasJoinRequest,
    isLoading,
    refetchMembership: checkMembership,
    refetchJoinRequest: checkMembership,
  }
}
