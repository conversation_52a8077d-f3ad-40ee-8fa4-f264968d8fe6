import { useState, useEffect, useCallback } from 'react'
import { useAccount, useReadContract, useReadContracts } from 'wagmi'
import { STORAGE_POOL_ABI } from '@/config/abis'
import { STORAGE_POOL_CONTRACT_ADDRESSES } from '@/config/contracts'
import { useChainId } from 'wagmi'

export interface PoolInfo {
  id: number
  name: string
  region: string
  requiredTokens: bigint
  minPingTime: bigint
  maxChallengeResponsePeriod: bigint
  creator: string
  creatorPeerId: string
  isActive: boolean
  memberCount: number
  members: string[]
  joinRequests: string[]
}

export interface JoinRequest {
  poolId: number
  user: string
  timestamp?: number
}

export function useStoragePoolData() {
  const { address } = useAccount()
  const chainId = useChainId()
  const [pools, setPools] = useState<PoolInfo[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const contractAddress = STORAGE_POOL_CONTRACT_ADDRESSES[chainId]

  // Get pool count
  const { data: poolCount, refetch: refetchPoolCount } = useReadContract({
    address: contractAddress,
    abi: STORAGE_POOL_ABI,
    functionName: 'getPoolCount',
    query: {
      enabled: !!contractAddress,
    },
  })

  // Fetch all pool data
  const fetchPoolsData = useCallback(async () => {
    if (!contractAddress || !poolCount || poolCount === 0n) {
      setPools([])
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const poolIds = Array.from({ length: Number(poolCount) }, (_, i) => i)
      
      // Prepare contract calls for all pools
      const poolInfoCalls = poolIds.map(id => ({
        address: contractAddress,
        abi: STORAGE_POOL_ABI,
        functionName: 'getPoolInfo',
        args: [id],
      }))

      const memberCountCalls = poolIds.map(id => ({
        address: contractAddress,
        abi: STORAGE_POOL_ABI,
        functionName: 'getPoolMemberCount',
        args: [id],
      }))

      const membersCalls = poolIds.map(id => ({
        address: contractAddress,
        abi: STORAGE_POOL_ABI,
        functionName: 'getPoolMembers',
        args: [id],
      }))

      const joinRequestsCalls = poolIds.map(id => ({
        address: contractAddress,
        abi: STORAGE_POOL_ABI,
        functionName: 'getJoinRequests',
        args: [id],
      }))

      // Execute all calls
      const [poolInfoResults, memberCountResults, membersResults, joinRequestsResults] = await Promise.all([
        // Note: In a real implementation, you'd use useReadContracts or similar
        // For now, we'll simulate the structure
        Promise.resolve(poolInfoCalls.map(() => ({ result: null, status: 'success' }))),
        Promise.resolve(memberCountCalls.map(() => ({ result: 0n, status: 'success' }))),
        Promise.resolve(membersCalls.map(() => ({ result: [], status: 'success' }))),
        Promise.resolve(joinRequestsCalls.map(() => ({ result: [], status: 'success' }))),
      ])

      const poolsData: PoolInfo[] = poolIds.map((id, index) => {
        const poolInfo = poolInfoResults[index]?.result as any
        const memberCount = memberCountResults[index]?.result as bigint
        const members = membersResults[index]?.result as string[]
        const joinRequests = joinRequestsResults[index]?.result as string[]

        return {
          id,
          name: poolInfo?.[0] || `Pool ${id}`,
          region: poolInfo?.[1] || 'Unknown',
          requiredTokens: poolInfo?.[2] || 0n,
          minPingTime: poolInfo?.[3] || 0n,
          maxChallengeResponsePeriod: poolInfo?.[4] || 0n,
          creator: poolInfo?.[5] || '0x0',
          creatorPeerId: poolInfo?.[6] || '',
          isActive: poolInfo?.[7] || false,
          memberCount: Number(memberCount || 0n),
          members: members || [],
          joinRequests: joinRequests || [],
        }
      })

      setPools(poolsData)
    } catch (err) {
      console.error('Error fetching pools data:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch pools data')
    } finally {
      setIsLoading(false)
    }
  }, [contractAddress, poolCount])

  // Fetch specific pool data
  const fetchPoolData = useCallback(async (poolId: number): Promise<PoolInfo | null> => {
    if (!contractAddress) return null

    try {
      // In a real implementation, you'd make actual contract calls here
      // For now, return from cached data or make individual calls
      const existingPool = pools.find(p => p.id === poolId)
      if (existingPool) return existingPool

      // Make individual contract calls for this pool
      return null
    } catch (err) {
      console.error(`Error fetching pool ${poolId} data:`, err)
      return null
    }
  }, [contractAddress, pools])

  // Get join requests for all pools
  const getAllJoinRequests = useCallback((): JoinRequest[] => {
    const allRequests: JoinRequest[] = []
    
    pools.forEach(pool => {
      pool.joinRequests.forEach(user => {
        allRequests.push({
          poolId: pool.id,
          user,
        })
      })
    })

    return allRequests
  }, [pools])

  // Refresh all data
  const refreshData = useCallback(async () => {
    await refetchPoolCount()
    await fetchPoolsData()
  }, [refetchPoolCount, fetchPoolsData])

  // Auto-fetch when pool count changes
  useEffect(() => {
    if (poolCount !== undefined) {
      fetchPoolsData()
    }
  }, [poolCount, fetchPoolsData])

  return {
    pools,
    isLoading,
    error,
    poolCount: Number(poolCount || 0n),
    fetchPoolData,
    getAllJoinRequests,
    refreshData,
  }
}

// Hook for checking if user is member of specific pool
export function usePoolMembership(poolId: number, userAddress?: string) {
  const chainId = useChainId()
  const contractAddress = STORAGE_POOL_CONTRACT_ADDRESSES[chainId]

  const { data: isMember, refetch: refetchMembership } = useReadContract({
    address: contractAddress,
    abi: STORAGE_POOL_ABI,
    functionName: 'isPoolMember',
    args: [poolId, userAddress],
    query: {
      enabled: !!contractAddress && !!userAddress && poolId >= 0,
    },
  })

  const { data: hasJoinRequest, refetch: refetchJoinRequest } = useReadContract({
    address: contractAddress,
    abi: STORAGE_POOL_ABI,
    functionName: 'hasJoinRequest',
    args: [poolId, userAddress],
    query: {
      enabled: !!contractAddress && !!userAddress && poolId >= 0,
    },
  })

  return {
    isMember: !!isMember,
    hasJoinRequest: !!hasJoinRequest,
    refetchMembership,
    refetchJoinRequest,
  }
}
