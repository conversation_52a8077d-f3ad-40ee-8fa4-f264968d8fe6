import { useState, useEffect, useCallback } from 'react'
import { useAccount, useReadContract, useReadContracts } from 'wagmi'
import { STORAGE_POOL_ABI } from '@/config/abis'
import { STORAGE_POOL_CONTRACT_ADDRESSES } from '@/config/contracts'
import { useChainId } from 'wagmi'

export interface PoolInfo {
  id: number
  name: string
  region: string
  requiredTokens: bigint
  minPingTime: bigint
  maxChallengeResponsePeriod: bigint
  creator: string
  creatorPeerId: string
  isActive: boolean
  memberCount: number
  members: string[]
  joinRequests: string[]
}

export interface JoinRequest {
  poolId: number
  user: string
  timestamp?: number
}

export function useStoragePoolData() {
  const { address } = useAccount()
  const chainId = useChainId()
  const [pools, setPools] = useState<PoolInfo[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const contractAddress = STORAGE_POOL_CONTRACT_ADDRESSES[chainId]

  // Get pool count
  const { data: poolCount, refetch: refetchPoolCount } = useReadContract({
    address: contractAddress,
    abi: STORAGE_POOL_ABI,
    functionName: 'poolCount',
    query: {
      enabled: !!contractAddress,
    },
  })

  // Fetch all pool data
  const fetchPoolsData = useCallback(async () => {
    if (!contractAddress || !poolCount || poolCount === 0n) {
      setPools([])
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const poolIds = Array.from({ length: Number(poolCount) }, (_, i) => i)
      const poolsData: PoolInfo[] = []

      // Fetch each pool's data
      for (const poolId of poolIds) {
        try {
          // Get pool basic info
          const poolInfo = await fetch(`/api/contract/read`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              address: contractAddress,
              abi: STORAGE_POOL_ABI,
              functionName: 'pools',
              args: [poolId],
            }),
          }).then(res => res.json()).catch(() => null)

          if (!poolInfo) continue

          // Get join request count
          const joinRequestCount = await fetch(`/api/contract/read`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              address: contractAddress,
              abi: STORAGE_POOL_ABI,
              functionName: 'getJoinRequestCount',
              args: [poolId],
            }),
          }).then(res => res.json()).catch(() => 0)

          // Get members (simplified - in real implementation you'd paginate)
          const members: string[] = []
          const memberCount = Number(poolInfo[8] || 0)
          for (let i = 0; i < Math.min(memberCount, 100); i++) {
            try {
              const member = await fetch(`/api/contract/read`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  address: contractAddress,
                  abi: STORAGE_POOL_ABI,
                  functionName: 'poolMembers',
                  args: [poolId, i],
                }),
              }).then(res => res.json())
              if (member) members.push(member)
            } catch {
              break
            }
          }

          // Get join requests (simplified)
          const joinRequests: string[] = []
          const joinReqCount = Number(joinRequestCount || 0)
          for (let i = 0; i < Math.min(joinReqCount, 100); i++) {
            try {
              const request = await fetch(`/api/contract/read`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  address: contractAddress,
                  abi: STORAGE_POOL_ABI,
                  functionName: 'joinRequests',
                  args: [poolId, i],
                }),
              }).then(res => res.json())
              if (request) joinRequests.push(request)
            } catch {
              break
            }
          }

          poolsData.push({
            id: poolId,
            name: poolInfo[0] || `Pool ${poolId}`,
            region: poolInfo[1] || 'Unknown',
            requiredTokens: BigInt(poolInfo[2] || 0),
            minPingTime: BigInt(poolInfo[3] || 0),
            maxChallengeResponsePeriod: BigInt(poolInfo[4] || 0),
            creator: poolInfo[5] || '0x0',
            creatorPeerId: poolInfo[6] || '',
            isActive: poolInfo[7] || false,
            memberCount,
            members,
            joinRequests,
          })
        } catch (err) {
          console.error(`Error fetching pool ${poolId}:`, err)
        }
      }

      setPools(poolsData)
    } catch (err) {
      console.error('Error fetching pools data:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch pools data')
    } finally {
      setIsLoading(false)
    }
  }, [contractAddress, poolCount])

  // Fetch specific pool data
  const fetchPoolData = useCallback(async (poolId: number): Promise<PoolInfo | null> => {
    if (!contractAddress) return null

    try {
      // In a real implementation, you'd make actual contract calls here
      // For now, return from cached data or make individual calls
      const existingPool = pools.find(p => p.id === poolId)
      if (existingPool) return existingPool

      // Make individual contract calls for this pool
      return null
    } catch (err) {
      console.error(`Error fetching pool ${poolId} data:`, err)
      return null
    }
  }, [contractAddress, pools])

  // Get join requests for all pools
  const getAllJoinRequests = useCallback((): JoinRequest[] => {
    const allRequests: JoinRequest[] = []
    
    pools.forEach(pool => {
      pool.joinRequests.forEach(user => {
        allRequests.push({
          poolId: pool.id,
          user,
        })
      })
    })

    return allRequests
  }, [pools])

  // Refresh all data
  const refreshData = useCallback(async () => {
    await refetchPoolCount()
    await fetchPoolsData()
  }, [refetchPoolCount, fetchPoolsData])

  // Auto-fetch when pool count changes
  useEffect(() => {
    if (poolCount !== undefined) {
      fetchPoolsData()
    }
  }, [poolCount, fetchPoolsData])

  return {
    pools,
    isLoading,
    error,
    poolCount: Number(poolCount || 0n),
    fetchPoolData,
    getAllJoinRequests,
    refreshData,
  }
}

// Hook for checking if user is member of specific pool
export function usePoolMembership(poolId: number, userAddress?: string) {
  const chainId = useChainId()
  const contractAddress = STORAGE_POOL_CONTRACT_ADDRESSES[chainId]

  const { data: isMember, refetch: refetchMembership } = useReadContract({
    address: contractAddress,
    abi: STORAGE_POOL_ABI,
    functionName: 'isPoolMember',
    args: [BigInt(poolId), userAddress as `0x${string}`],
    query: {
      enabled: !!contractAddress && !!userAddress && poolId >= 0,
    },
  })

  const { data: hasJoinRequest, refetch: refetchJoinRequest } = useReadContract({
    address: contractAddress,
    abi: STORAGE_POOL_ABI,
    functionName: 'hasJoinRequest',
    args: [BigInt(poolId), userAddress as `0x${string}`],
    query: {
      enabled: !!contractAddress && !!userAddress && poolId >= 0,
    },
  })

  return {
    isMember: !!isMember,
    hasJoinRequest: !!hasJoinRequest,
    refetchMembership,
    refetchJoinRequest,
  }
}
