server {
    server_name claim-ui.fula.network;
    location /_next/static/ {
        alias /home/<USER>/claim-ui/.next/static/;
        access_log off; # Optional: Disable access logs for static files
        expires max;    # Optional: Cache static files for performance
    }
    location /favicon.png {
        alias /home/<USER>/claim-ui/public/favicon.png;
        access_log off; # Optional: Disable access logs for static files
        expires max;    # Optional: Cache static files for performance
    }
    location /images/ {
        alias /home/<USER>/claim-ui/public/images/;
        access_log off; # Optional: Disable access logs for static files
        expires max;    # Optional: Cache static files for performance
    }
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }


    listen 80;
}